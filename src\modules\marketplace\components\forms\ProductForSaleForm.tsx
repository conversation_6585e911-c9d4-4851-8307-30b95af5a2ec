import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Eye, Code } from 'lucide-react';
import {
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Icon,
  Typography,
  Card,
} from '@/shared/components/common';
import { EmailBuilder } from '@/shared/components/email-builder';
import { SubmitHandler } from 'react-hook-form';
import { ProductStatus, ProductCategory } from '../../types/product-for-sale.types';

// Định nghĩa type cho form values
export interface ProductForSaleFormValues {
  name: string;
  description?: string;
  image?: string;
  price?: number;
  category: ProductCategory;
  detail?: string;
  userManual?: string;
}


// Interface cho ADD image operation
interface AddImageOperation {
  operation: 'ADD';
  index: number;
  mimeType: string;
}

// Interface cho DELETE image operation
interface DeleteImageOperation {
  operation: 'DELETE';
  key: string;
}

// Union type cho image operations
type ImageOperation = AddImageOperation | DeleteImageOperation;

interface ProductForSaleFormProps {
  onSubmit: (
    values: ProductForSaleFormValues & {
      imageFiles?: File[]; // Danh sách file ảnh mới để upload
      imageOperations?: ImageOperation[]; // Operations cho ảnh (ADD/DELETE)
      hasImageChanged?: boolean; // Flag để biết ảnh có thay đổi không
      hasDetailChanged?: boolean; // Flag để biết detail có thay đổi không
      hasUserManualChanged?: boolean; // Flag để biết userManual có thay đổi không
    },
    submitForApproval?: boolean
  ) => void;
  onCancel: () => void;
  initialValues?: Partial<
    ProductForSaleFormValues & {
      oldImageKeys?: string[]; // Keys thực tế của ảnh gốc
      images?: string[]; // URLs của ảnh để hiển thị
      detail?: string;
      userManual?: string;
    }
  >;
  isSubmitting?: boolean;
}

/**
 * Form thêm/sửa sản phẩm đăng bán
 */
const ProductForSaleForm: React.FC<ProductForSaleFormProps> = React.memo(({
  onSubmit,
  onCancel,
  initialValues,
  isSubmitting = false,
}) => {
  const { t } = useTranslation('marketplace');

  // Tạo schema với translation

  // State cho image upload - hỗ trợ nhiều ảnh
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [hasImageChanged, setHasImageChanged] = useState<boolean>(false);
  const [deletedImageKeys, setDeletedImageKeys] = useState<string[]>([]); // Track ảnh bị xóa

  // Track mapping giữa preview và key để tránh lỗi index
  const [imageKeyMapping, setImageKeyMapping] = useState<Map<string, string>>(new Map());

  // Track thay đổi detail và userManual
  const [hasDetailChanged, setHasDetailChanged] = useState<boolean>(false);
  const [hasUserManualChanged, setHasUserManualChanged] = useState<boolean>(false);
  const originalDetail = initialValues?.detail || '';
  const originalUserManual = initialValues?.userManual || '';

  // State cho preview mode của EmailBuilder
  const [detailPreviewMode, setDetailPreviewMode] = useState<'design' | 'code'>('design');
  const [userManualPreviewMode, setUserManualPreviewMode] = useState<'design' | 'code'>('design');

  // State cho nội dung detail và userManual
  const [detailContent, setDetailContent] = useState<string>('');
  const [userManualContent, setUserManualContent] = useState<string>('');


  // Khởi tạo mapping và previews khi component mount
  useEffect(() => {
    console.log('🔍 [INIT] Initializing with images:', initialValues?.images);
    console.log('🔍 [INIT] Old image keys:', initialValues?.oldImageKeys);

    if (initialValues?.images && Array.isArray(initialValues.images)) {
      const mapping = new Map<string, string>();
      const previews: string[] = [];

      // Sử dụng oldImageKeys để mapping đúng key
      const oldKeys = initialValues.oldImageKeys || [];

      initialValues.images.forEach((imageUrl: string, index: number) => {
        if (typeof imageUrl === 'string' && imageUrl.trim()) {
          // Lấy key thực tế từ oldImageKeys
          const realKey = oldKeys[index];
          if (realKey) {
            console.log(`🔍 [INIT] Mapping URL "${imageUrl}" to key "${realKey}"`);
            mapping.set(imageUrl, realKey);
          } else {
            console.warn(`⚠️ [INIT] No key found for image ${index}:`, imageUrl);
          }
          previews.push(imageUrl);
        }
      });

      setImageKeyMapping(mapping);
      setImagePreviews(previews);
      console.log('🔍 [INIT] Final mapping:', mapping);
      console.log('🔍 [INIT] Final previews:', previews);
    }
  }, [initialValues?.images, initialValues?.oldImageKeys]);

  // Helper function để chuyển HTML thành text
  const htmlToText = (html: string): string => {
    // Tạo một div tạm để parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  // Khởi tạo content từ originalDetail và originalUserManual
  useEffect(() => {
    const fetchContentFromUrl = async (url: string) => {
      if (!url) return '';

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: { Accept: 'text/plain, text/html, */*' },
          mode: 'cors',
          cache: 'no-cache',
        });

        if (response.ok) {
          return await response.text();
        }
        return '';
      } catch (error) {
        console.error('Failed to fetch content:', error);
        return '';
      }
    };

    const initializeContent = async () => {
      // Xử lý detail content
      if (originalDetail) {
        const isDetailUrl = originalDetail.startsWith('https://') && originalDetail.includes('cdn.redai.vn');
        if (isDetailUrl) {
          const content = await fetchContentFromUrl(originalDetail);
          setDetailContent(content || originalDetail);
        } else {
          setDetailContent(originalDetail);
        }
      } else {
        setDetailContent('');
      }

      // Xử lý userManual content
      if (originalUserManual) {
        const isUserManualUrl = originalUserManual.startsWith('https://') && originalUserManual.includes('cdn.redai.vn');
        if (isUserManualUrl) {
          const content = await fetchContentFromUrl(originalUserManual);
          // Với userManual, nếu content là HTML thì chuyển thành text
          const finalContent = content || originalUserManual;
          if (finalContent.includes('<') && finalContent.includes('>')) {
            // Có vẻ là HTML, chuyển thành text
            const textContent = htmlToText(finalContent);
            setUserManualContent(textContent);
          } else {
            // Đã là text
            setUserManualContent(finalContent);
          }
        } else {
          setUserManualContent(originalUserManual);
        }
      } else {
        setUserManualContent('');
      }
    };

    initializeContent();
  }, [originalDetail, originalUserManual, htmlToText]);

  // Helper function để chuyển HTML thành text
  const htmlToText = (html: string): string => {
    // Tạo một div tạm để parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  // Callback cho EmailBuilder content change
  const handleDetailEmailBuilderChange = React.useCallback((html: string) => {
    // Với detail, giữ nguyên HTML format
    setDetailContent(html);
    const hasChanged = html.trim() !== originalDetail.trim();
    setHasDetailChanged(hasChanged);
  }, [originalDetail]);

  const handleUserManualEmailBuilderChange = React.useCallback((html: string) => {
    // Với userManual, chuyển HTML thành text format
    const textContent = htmlToText(html);
    setUserManualContent(textContent);
    const hasChanged = textContent.trim() !== originalUserManual.trim();
    setHasUserManualChanged(hasChanged);
  }, [originalUserManual]);

  // Xử lý khi thêm ảnh mới - tối ưu với useCallback để không giật
  const handleAddImage = useCallback((file: File, dataUrl: string) => {
    console.log('🔍 [ADD IMAGE] Adding new image:', { fileName: file.name, fileSize: file.size, fileType: file.type });

    // Batch update để tránh re-render nhiều lần
    setImageFiles(prev => [...prev, file]);
    setImagePreviews(prev => [...prev, dataUrl]);
    setHasImageChanged(true);

    console.log('✅ [ADD IMAGE] Image added successfully');
  }, []);

  // Xử lý khi xóa ảnh - tối ưu với useCallback để không giật
  const handleRemoveImage = useCallback((index: number) => {
    console.log('🔍 [REMOVE IMAGE] Removing image at index:', index);

    // Lấy URL của ảnh đang xóa
    const imageUrl = imagePreviews[index];
    const originalImageCount = (initialValues?.images?.length || 0);

    // Kiểm tra xem có phải ảnh cũ không
    const correspondingKey = imageKeyMapping.get(imageUrl);

    if (correspondingKey) {
      // Ảnh gốc - thêm vào danh sách xóa
      console.log('�️ [REMOVE IMAGE] Deleting original image with key:', correspondingKey);
      setDeletedImageKeys(prev => [...prev, correspondingKey]);
      setImageKeyMapping(prev => {
        const newMapping = new Map(prev);
        newMapping.delete(imageUrl);
        return newMapping;
      });
    } else if (index >= originalImageCount) {
      // Ảnh mới - xóa khỏi imageFiles
      const newImageIndex = index - originalImageCount;
      console.log('�️ [REMOVE IMAGE] Removing new image at imageFiles index:', newImageIndex);
      setImageFiles(prev => prev.filter((_, i) => i !== newImageIndex));
    }

    // Xóa khỏi imagePreviews
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
    setHasImageChanged(true);

    console.log('✅ [REMOVE IMAGE] Image removed successfully');
  }, [imagePreviews, imageKeyMapping, initialValues?.images?.length]);

  // Xử lý khi submit form (lưu draft)
  const handleFormSubmit: SubmitHandler<ProductForSaleFormValues> = values => {
    // Tạo image operations
    const imageOperations: ImageOperation[] = [];

    // Thêm operations cho ảnh mới (ADD) - chỉ gửi các field backend yêu cầu
    imageFiles.forEach((file, index) => {
      imageOperations.push({
        operation: 'ADD',
        index: index,
        mimeType: file.type
        // Không gửi size và name vì backend không cho phép
      });
    });

    // Thêm operations cho ảnh bị xóa (DELETE)
    deletedImageKeys.forEach(key => {
      imageOperations.push({
        operation: 'DELETE',
        key: key
      });
    });

    console.log('🔍 [ProductForSaleForm] Image operations:', imageOperations);

    // Đối với tạo mới, luôn coi như có thay đổi nếu có nội dung
    const isCreating = !initialValues?.name;
    const finalHasDetailChanged = isCreating
      ? !!(detailContent && detailContent.trim())
      : hasDetailChanged;
    const finalHasUserManualChanged = isCreating
      ? !!(userManualContent && userManualContent.trim())
      : hasUserManualChanged;

    console.log('🔍 [ProductForSaleForm] Final hasDetailChanged:', finalHasDetailChanged);
    console.log('🔍 [ProductForSaleForm] Final hasUserManualChanged:', finalHasUserManualChanged);

    onSubmit(
      {
        ...values,
        detail: detailContent, // Sử dụng detailContent từ EmailBuilder
        userManual: userManualContent, // Sử dụng userManualContent từ EmailBuilder
        imageFiles, // Danh sách file ảnh mới để upload
        imageOperations, // Operations cho ảnh (ADD/DELETE)
        hasImageChanged, // Flag để biết ảnh có thay đổi không
        hasDetailChanged: finalHasDetailChanged, // Flag để biết detail có thay đổi không
        hasUserManualChanged: finalHasUserManualChanged, // Flag để biết userManual có thay đổi không
        status: ProductStatus.ACTIVE,
      } as ProductForSaleFormValues & {
        imageFiles?: File[];
        imageOperations?: ImageOperation[];
        hasImageChanged?: boolean;
        hasDetailChanged?: boolean;
        hasUserManualChanged?: boolean;
      },
      false
    );
  };

  // Xử lý khi submit form (gửi duyệt)
  const handleSubmitForApproval: SubmitHandler<ProductForSaleFormValues> = values => {
    // Validation: Kiểm tra có ảnh không
    if (imageFiles.length === 0 && imagePreviews.length === 0) {
      alert(
        t('productsForSale.form.validation.imageRequired', 'Vui lòng chọn ít nhất một ảnh sản phẩm')
      );
      return;
    }

    // Tạo image operations
    const imageOperations: ImageOperation[] = [];

    // Thêm operations cho ảnh mới (ADD) - chỉ gửi các field backend yêu cầu
    imageFiles.forEach((file, index) => {
      imageOperations.push({
        operation: 'ADD',
        index: index,
        mimeType: file.type
        // Không gửi size và name vì backend không cho phép
      });
    });

    // Thêm operations cho ảnh bị xóa (DELETE)
    deletedImageKeys.forEach(key => {
      imageOperations.push({
        operation: 'DELETE',
        key: key
      });
    });

    // Đối với tạo mới, luôn coi như có thay đổi nếu có nội dung
    const isCreating = !initialValues?.name;
    const finalHasDetailChanged = isCreating
      ? !!(detailContent && detailContent.trim())
      : hasDetailChanged;
    const finalHasUserManualChanged = isCreating
      ? !!(userManualContent && userManualContent.trim())
      : hasUserManualChanged;

    onSubmit(
      {
        ...values,
        detail: detailContent, // Sử dụng detailContent từ EmailBuilder
        userManual: userManualContent, // Sử dụng userManualContent từ EmailBuilder
        imageFiles, // Danh sách file ảnh mới để upload
        imageOperations, // Operations cho ảnh (ADD/DELETE)
        hasImageChanged, // Flag để biết ảnh có thay đổi không
        hasDetailChanged: finalHasDetailChanged, // Flag để biết detail có thay đổi không
        hasUserManualChanged: finalHasUserManualChanged, // Flag để biết userManual có thay đổi không
        status: ProductStatus.ACTIVE,
      } as ProductForSaleFormValues & {
        imageFiles?: File[];
        imageOperations?: ImageOperation[];
        hasImageChanged?: boolean;
        hasDetailChanged?: boolean;
        hasUserManualChanged?: boolean;
      },
      true
    );
  };

  // Giả lập URL ảnh ngẫu nhiên
  const getRandomImageUrl = () => {
    return `/images/products/product-${Math.floor(Math.random() * 10) + 1}.jpg`;
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h4">
          {initialValues?.name
            ? t('productsForSale.editProduct', 'Chỉnh sửa sản phẩm')
            : t('productsForSale.addProduct', 'Thêm sản phẩm mới')}
        </Typography>

        <Form
          onSubmit={handleFormSubmit as unknown as SubmitHandler<Record<string, unknown>>}
          defaultValues={{
            ...initialValues,
            image: initialValues?.image || getRandomImageUrl(),
            detail: detailContent,
            userManual: userManualContent,
          }}
          className="space-y-6"
        >
          <FormItem name="name" label={t('marketplace:product.form.name', 'Tên sản phẩm')} required>
            <Input
              placeholder={t('marketplace:product.form.namePlaceholder', 'Nhập tên sản phẩm')}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('marketplace:product.form.description', 'Mô tả sản phẩm')}
          >
            <Input
              placeholder={t('marketplace:product.form.descriptionPlaceholder', 'Nhập mô tả sản phẩm')}
              fullWidth
            />
          </FormItem>

          <FormItem name="image" label={t('marketplace:product.form.images', 'Ảnh sản phẩm')} required>
            <div className="space-y-4">
              {/* Upload ảnh mới */}
              <div className="border-2 border-dashed border-muted rounded-lg p-6 text-center hover:border-primary/50 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={e => {
                    const files = Array.from(e.target.files || []);
                    console.log('🔍 [FILE INPUT] Selected files:', files.map(f => f.name));

                    // Process files immediately để hiện ảnh ngay
                    files.forEach((file, index) => {
                      // Validate file type
                      if (!file.type.startsWith('image/')) {
                        console.warn('⚠️ [FILE INPUT] Invalid file type:', file.type);
                        return;
                      }

                      const reader = new FileReader();
                      reader.onload = event => {
                        if (event.target?.result) {
                          console.log(`✅ [FILE INPUT] File ${index + 1} loaded successfully`);
                          handleAddImage(file, event.target.result as string);
                        }
                      };
                      reader.onerror = () => {
                        console.error('❌ [FILE INPUT] Error reading file:', file.name);
                      };
                      reader.readAsDataURL(file);
                    });

                    // Reset input để có thể chọn lại cùng file
                    e.target.value = '';
                  }}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer block">
                  <Icon name="upload" size="lg" className="mx-auto mb-3 text-muted" />
                  <Typography variant="body1" className="mb-1">
                    {t('marketplace:product.form.selectImages', 'Chọn ảnh sản phẩm')}
                  </Typography>
                  <Typography variant="body2" className="text-muted">
                    {t('marketplace:product.form.imagesHelp', 'Hỗ trợ nhiều ảnh, định dạng: JPG, PNG')}
                  </Typography>
                </label>
              </div>

              {/* Hiển thị ảnh đã chọn */}
              {imagePreviews.length > 0 && (
                <div>
                  <Typography variant="h6" className="mb-4">
                    {t('marketplace:product.form.selectedImagesTitle', 'Ảnh đã chọn ({{count}})', { count: imagePreviews.length })}
                  </Typography>
                  <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-4">
                    {imagePreviews.map((preview, index) => (
                      <div key={`image-${index}`} className="relative group">
                        <div className="aspect-square rounded-lg overflow-hidden border border-muted">
                          <img
                            src={preview.startsWith('data:') || preview.startsWith('http') ? preview : `https://cdn.redai.vn/${preview}`}
                            alt={`Product ${index + 1}`}
                            className="w-full h-full object-cover"
                            loading="lazy"
                            onError={(e) => {
                              console.warn('🔍 [IMAGE ERROR] Failed to load image:', preview);
                              // Fallback to a placeholder or hide the image
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </div>
                        <button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleRemoveImage(index);
                          }}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg hover:bg-red-600 hover:scale-110"
                          title={t('marketplace:product.form.removeImage', 'Xóa ảnh')}
                        >
                          <Icon name="x" size="xs" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Thông tin file mới */}
              {imageFiles.length > 0 && (
                <div className="text-sm text-muted bg-muted/20 p-3 rounded-lg">
                  <p>{t('marketplace:product.form.selectedImages', 'Đã chọn {{count}} ảnh mới', { count: imageFiles.length })}</p>
                </div>
              )}
            </div>
          </FormItem>

          <FormItem name="price" label={t('marketplace:product.form.price', 'Giá bán (rpoint)')}>
            <Input
              type="number"
              placeholder={t(
                'marketplace:product.form.pricePlaceholder',
                'Nhập giá bán (để trống nếu không thay đổi)'
              )}
              min={0}
              step={1000}
              leftIcon={<Icon name="dollar-sign" size="sm" />}
              fullWidth
            />
          </FormItem>

          <FormItem name="category" label={t('marketplace:product.form.category', 'Thể loại')} required>
            <Select
              placeholder={t('marketplace:product.form.categoryPlaceholder', 'Chọn thể loại')}
              options={[
                {
                  value: ProductCategory.AGENT,
                  label: t('marketplace:productsForSale.categories.agent', 'AI Agent'),
                },
                {
                  value: ProductCategory.KNOWLEDGE_FILE,
                  label: t('marketplace:productsForSale.categories.knowledgeFile', 'Knowledge File'),
                },
                {
                  value: ProductCategory.FUNCTION,
                  label: t('marketplace:productsForSale.categories.function', 'Function'),
                },
                {
                  value: ProductCategory.FINETUNE,
                  label: t('marketplace:productsForSale.categories.finetune', 'Fine-tuned Model'),
                },
                {
                  value: ProductCategory.STRATEGY,
                  label: t('marketplace:productsForSale.categories.strategy', 'Strategy'),
                },
              ]}
              fullWidth
            />
          </FormItem>

          {/* Thêm input cho detail và userManual - hiển thị cho cả tạo mới và edit */}
          <Typography variant="h6">
            {initialValues?.name
              ? t('marketplace:product.form.additionalInfo', 'Thông tin bổ sung')
              : t('marketplace:product.form.productContent', 'Nội dung sản phẩm')}
          </Typography>

          <Card
            title={t('marketplace:product.form.detail', 'Thông tin chi tiết sản phẩm')}
            extra={
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant={detailPreviewMode === 'design' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setDetailPreviewMode('design')}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  {t('marketing:email.templates.form.content.designMode', 'Design')}
                </Button>
                <Button
                  type="button"
                  variant={detailPreviewMode === 'code' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setDetailPreviewMode('code')}
                >
                  <Code className="h-4 w-4 mr-1" />
                  {t('marketing:email.templates.form.content.codeMode', 'HTML')}
                </Button>
              </div>
            }
          >
            {detailPreviewMode === 'code' ? (
              <FormItem
                label={t('marketplace:product.form.detail', 'Thông tin chi tiết sản phẩm')}
                name="detail"
              >
                <textarea
                  className="w-full p-3 border border-border rounded-md min-h-[400px] font-mono text-sm bg-card-muted text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                  placeholder={
                    initialValues?.name
                      ? t(
                          'marketplace:product.form.detailPlaceholderEdit',
                          'Nhập thông tin chi tiết sản phẩm (nếu có thay đổi)'
                        )
                      : t(
                          'marketplace:product.form.detailPlaceholderCreate',
                          'Nhập thông tin chi tiết sản phẩm'
                        )
                  }
                  value={detailContent}
                  onChange={(e) => {
                    setDetailContent(e.target.value);
                    const hasChanged = e.target.value.trim() !== originalDetail.trim();
                    setHasDetailChanged(hasChanged);
                  }}
                />
              </FormItem>
            ) : (
              <div className="border border-border rounded-lg overflow-hidden min-h-[500px] bg-card">
                <EmailBuilder
                  initialValue={detailContent}
                  onContentChange={handleDetailEmailBuilderChange}
                />
              </div>
            )}
          </Card>

          <Card
            title={t('marketplace:product.form.userManual', 'Hướng dẫn sử dụng')}
            extra={
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant={userManualPreviewMode === 'design' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setUserManualPreviewMode('design')}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  {t('marketing:email.templates.form.content.designMode', 'Design')}
                </Button>
                <Button
                  type="button"
                  variant={userManualPreviewMode === 'code' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setUserManualPreviewMode('code')}
                >
                  <Code className="h-4 w-4 mr-1" />
                  {t('marketing:email.templates.form.content.codeMode', 'HTML')}
                </Button>
              </div>
            }
          >
            {userManualPreviewMode === 'code' ? (
              <FormItem
                label={t('marketplace:product.form.userManual', 'Hướng dẫn sử dụng')}
                name="userManual"
              >
                <textarea
                  className="w-full p-3 border border-border rounded-md min-h-[400px] font-mono text-sm bg-card-muted text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                  placeholder={
                    initialValues?.name
                      ? t(
                          'marketplace:product.form.userManualPlaceholderEdit',
                          'Nhập hướng dẫn sử dụng sản phẩm (nếu có thay đổi)'
                        )
                      : t(
                          'marketplace:product.form.userManualPlaceholderCreate',
                          'Nhập hướng dẫn sử dụng sản phẩm'
                        )
                  }
                  value={userManualContent}
                  onChange={(e) => {
                    // Với userManual, luôn lưu dưới dạng text
                    const textValue = e.target.value;
                    setUserManualContent(textValue);
                    const hasChanged = textValue.trim() !== originalUserManual.trim();
                    setHasUserManualChanged(hasChanged);
                  }}
                />
              </FormItem>
            ) : (
              <div className="border border-border rounded-lg overflow-hidden min-h-[500px] bg-card">
                <EmailBuilder
                  initialValue={userManualContent}
                  onContentChange={handleUserManualEmailBuilderChange}
                />
              </div>
            )}
          </Card>

          <div className="flex justify-end space-x-4 mt-8">
            <Button variant="outline" onClick={onCancel} >
              {t('marketplace:common.cancel', 'Hủy')}
            </Button>
            <Button variant="outline" type="submit" disabled={isSubmitting}>
              {isSubmitting ? t('marketplace:common.submitting', 'Đang lưu...') : t('marketplace:common.save', 'Lưu nháp')}
            </Button>
            {initialValues?.name && (
              <Button
                variant="primary"
                type="button"
                disabled={isSubmitting}
                onClick={() => {
                  // Get form values and submit for approval
                  const form = document.querySelector('form');
                  if (form) {
                    const formData = new FormData(form);
                    const values = Object.fromEntries(
                      formData.entries()
                    ) as unknown as ProductForSaleFormValues;
                    handleSubmitForApproval(values);
                  }
                }}
              >
                {isSubmitting
                  ? t('marketplace:common.submitting', 'Đang gửi...')
                  : t('marketplace:common.submitForApproval', 'Gửi duyệt')}
              </Button>
            )}
          </div>
        </Form>
      </div>
    </Card>
  );
});

export default ProductForSaleForm;
